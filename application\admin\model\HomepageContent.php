<?php

namespace app\admin\model;

use think\Model;

class HomepageContent extends Model
{
    // 表名
    protected $name = 'homepage_content';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'type_text',
        'status_text'
    ];
    
    // 类型列表
    public function getTypeList()
    {
        return ['banner' => __('顶部图片'), 'notice' => __('公告')];
    }
    
    // 状态列表
    public function getStatusList()
    {
        return ['normal' => __('正常'), 'hidden' => __('隐藏')];
    }
    
    // 获取类型文本
    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    // 获取状态文本
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    // 获取顶部图片
    public static function getBanner()
    {
        return self::where('type', 'banner')
            ->where('status', 'normal')
            ->order('sort', 'asc')
            ->find();
    }
    
    // 获取公告列表
    public static function getNotices()
    {
        return self::where('type', 'notice')
            ->where('status', 'normal')
            ->order('sort', 'asc')
            ->select();
    }
}
