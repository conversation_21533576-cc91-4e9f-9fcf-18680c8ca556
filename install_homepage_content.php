<?php
// 临时脚本：创建首页内容管理表

// 定义应用目录
define('APP_PATH', __DIR__ . '/application/');

// 加载框架引导文件
require __DIR__ . '/thinkphp/start.php';

use think\Db;

try {
    // 创建表的SQL
    $sql = "
    CREATE TABLE IF NOT EXISTS `fa_homepage_content` (
      `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
      `type` enum('banner','notice') NOT NULL DEFAULT 'banner' COMMENT '内容类型:banner=顶部图片,notice=公告',
      `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
      `content` text COMMENT '内容',
      `image` varchar(255) DEFAULT '' COMMENT '图片路径',
      `url` varchar(255) DEFAULT '' COMMENT '链接地址',
      `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
      `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
      `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
      `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`),
      KEY `type` (`type`),
      KEY `status` (`status`),
      KEY `sort` (`sort`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='首页内容管理表';
    ";
    
    // 执行创建表
    Db::execute($sql);
    echo "✅ 数据库表 fa_homepage_content 创建成功！\n";
    
    // 插入默认数据
    $defaultData = [
        [
            'type' => 'banner',
            'title' => '首页顶部图片',
            'content' => '',
            'image' => '/images/6677168.gif',
            'url' => '',
            'sort' => 1,
            'status' => 'normal',
            'createtime' => time(),
            'updatetime' => time()
        ],
        [
            'type' => 'notice',
            'title' => '充值公告',
            'content' => '★ ★ ★ 充值请联系最新管理员微信号：xxxxxx ★ ★ ★',
            'image' => '',
            'url' => '',
            'sort' => 1,
            'status' => 'normal',
            'createtime' => time(),
            'updatetime' => time()
        ],
        [
            'type' => 'notice',
            'title' => '论坛规则',
            'content' => '★★★避免不必要的误会，请认真看完论坛规则★★★',
            'image' => '',
            'url' => '',
            'sort' => 2,
            'status' => 'normal',
            'createtime' => time(),
            'updatetime' => time()
        ]
    ];
    
    // 插入默认数据
    Db::name('homepage_content')->insertAll($defaultData);
    echo "✅ 默认数据插入成功！\n";
    
    echo "\n🎉 首页内容管理功能安装完成！\n";
    echo "📋 接下来需要在后台创建菜单\n";
    
} catch (Exception $e) {
    echo "❌ 安装失败：" . $e->getMessage() . "\n";
}
?>
