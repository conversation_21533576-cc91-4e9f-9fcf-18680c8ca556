<?php
// 检查表是否创建成功

// 定义应用目录
define('APP_PATH', __DIR__ . '/application/');

// 加载框架引导文件
require __DIR__ . '/thinkphp/start.php';

use think\Db;

try {
    // 检查表是否存在
    $tables = Db::query("SHOW TABLES LIKE 'fa_homepage_content'");
    
    if (empty($tables)) {
        echo "❌ 表 fa_homepage_content 不存在，开始创建...\n";
        
        // 创建表
        $sql = "
        CREATE TABLE `fa_homepage_content` (
          `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
          `type` enum('banner','notice') NOT NULL DEFAULT 'banner' COMMENT '内容类型:banner=顶部图片,notice=公告',
          `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
          `content` text COMMENT '内容',
          `image` varchar(255) DEFAULT '' COMMENT '图片路径',
          `url` varchar(255) DEFAULT '' COMMENT '链接地址',
          `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
          `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
          `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
          `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
          PRIMARY KEY (`id`),
          KEY `type` (`type`),
          KEY `status` (`status`),
          KEY `sort` (`sort`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首页内容管理表';
        ";
        
        Db::execute($sql);
        echo "✅ 表创建成功！\n";
        
        // 插入默认数据
        $data = [
            [
                'type' => 'banner',
                'title' => '首页顶部图片',
                'image' => '/images/6677168.gif',
                'sort' => 1,
                'status' => 'normal',
                'createtime' => time(),
                'updatetime' => time()
            ],
            [
                'type' => 'notice',
                'title' => '充值公告',
                'content' => '★ ★ ★ 充值请联系最新管理员微信号：xxxxxx ★ ★ ★',
                'sort' => 1,
                'status' => 'normal',
                'createtime' => time(),
                'updatetime' => time()
            ],
            [
                'type' => 'notice',
                'title' => '论坛规则',
                'content' => '★★★避免不必要的误会，请认真看完论坛规则★★★',
                'sort' => 2,
                'status' => 'normal',
                'createtime' => time(),
                'updatetime' => time()
            ]
        ];
        
        Db::name('homepage_content')->insertAll($data);
        echo "✅ 默认数据插入成功！\n";
        
    } else {
        echo "✅ 表 fa_homepage_content 已存在！\n";
    }
    
    // 检查数据
    $count = Db::name('homepage_content')->count();
    echo "📊 表中共有 {$count} 条数据\n";
    
    if ($count > 0) {
        $data = Db::name('homepage_content')->select();
        echo "📋 数据列表：\n";
        foreach ($data as $item) {
            echo "  - ID: {$item['id']}, 类型: {$item['type']}, 标题: {$item['title']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 错误：" . $e->getMessage() . "\n";
}
?>
