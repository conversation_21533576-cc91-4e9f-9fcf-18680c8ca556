define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'homepage_content/index' + location.search,
                    add_url: 'homepage_content/add',
                    edit_url: 'homepage_content/edit',
                    del_url: 'homepage_content/del',
                    multi_url: 'homepage_content/multi',
                    import_url: 'homepage_content/import',
                    table: 'homepage_content',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'type', title: __('Type'), searchList: {"banner":__('顶部图片'),"notice":__('公告')}, formatter: Table.api.formatter.normal},
                        {field: 'title', title: __('Title'), operate: 'LIKE'},
                        {field: 'content', title: __('Content'), operate: 'LIKE', formatter: function(value, row, index) {
                            if (row.type === 'notice') {
                                return value ? value.substr(0, 50) + '...' : '';
                            }
                            return '-';
                        }},
                        {field: 'image', title: __('Image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'sort', title: __('Sort')},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('正常'),"hidden":__('隐藏')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
                
                // 根据类型显示/隐藏字段
                $(document).on('change', '#c-type', function() {
                    var type = $(this).val();
                    if (type === 'banner') {
                        $('#image-group').show();
                        $('#content-group').hide();
                        $('#c-content').removeAttr('data-rule');
                    } else if (type === 'notice') {
                        $('#image-group').hide();
                        $('#content-group').show();
                        $('#c-content').attr('data-rule', 'required');
                    }
                });
                
                // 初始化时触发一次
                $('#c-type').trigger('change');
            }
        }
    };
    return Controller;
});
