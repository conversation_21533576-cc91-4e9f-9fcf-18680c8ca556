<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* 顶部图片区域 */
        .hero-section {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 0;
        }

        /* 标签区域 */
        .tabs-section {
            width: 100%;
            padding: 5px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
        }

        .tabs-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 5px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 2px 8px;
            margin: 0 2px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(153, 153, 153, 0.8);
            color: white;
            border: none;
            outline: none;
            line-height: 1.2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 未点击状态 - 灰色毛玻璃 */
        .tab-item:not(.active) {
            background: rgba(153, 153, 153, 0.8);
            color: white;
        }

        /* 点击状态颜色 - 毛玻璃效果 */
        .tab-item.active.tab-1 {
            background: rgba(40, 167, 69, 0.8); /* 绿色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-2 {
            background: rgba(0, 123, 255, 0.8); /* 蓝色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-3 {
            background: rgba(220, 53, 69, 0.8); /* 红色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-4 {
            background: rgba(52, 58, 64, 0.8); /* 黑色毛玻璃 */
            color: white;
        }

        /* 悬停效果 */
        .tab-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* 开奖信息区域 */
        .lottery-info {
            width: 100%;
            padding: 5px 15px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .lottery-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 开奖记录按钮 */
        .record-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #8B5CF6 0%, #FFFFFF 100%);
            color: #333;
            outline: none;
            box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .record-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.4);
        }

        /* 号码球区域 */
        .balls-section {
            width: 100%;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .balls-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: nowrap;
        }

        .ball-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 40px;
            flex-shrink: 0;
        }

        .ball-wrapper {
            position: relative;
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .ball-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .ball-number {
            position: absolute;
            top: calc(50% - 2px);
            left: 50%;
            transform: translate(-50%, -50%);
            color: black;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
        }

        .ball-info {
            text-align: center;
            font-size: 10px;
            color: #666;
            font-weight: 600;
            line-height: 1.2;
        }

        /* 加号分隔符 */
        .plus-separator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #28a745;
            font-size: 20px;
            font-weight: bold;
            margin: 0 2px;
            flex-shrink: 0;
        }

        /* 开奖时间区域 */
        .draw-time-section {
            width: 100%;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draw-time-text {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .draw-time-text .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 刷新按钮 */
        .refresh-btn {
            padding: 3px 10px;
            border: none;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
            outline: none;
            box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
        }

        /* 红色区域 */
        .red-section {
            width: 375px;
            min-height: 210px;
            background: #ed0000;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px 10px 15px 10px;
        }

        .red-content {
            display: flex;
            align-items: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .yellow-notice {
            color: #ffff00;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            line-height: 1.4;
        }

        .yellow-notice .small-text {
            font-size: 16px;
        }

        .arrow-icon {
            width: auto;
            height: auto;
            margin: 0 8px;
        }

        .arrow-left {
            transform: scaleX(-1);
        }

        /* 导航区域 */
        .nav-section {
            width: 375px;
            padding: 0;
            background: white;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
        }

        .nav-column {
            border-right: 1px solid #dee2e6;
        }

        .nav-column:last-child {
            border-right: none;
        }

        .nav-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: #f8f9fa;
        }

        .nav-item:last-child {
            border-bottom: none;
        }

        .nav-icon {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* 第一列红色 */
        .nav-column:nth-child(1) .nav-icon {
            background: #dc3545;
        }

        .nav-column:nth-child(1) .nav-text {
            color: #dc3545;
        }

        /* 第二列蓝色 */
        .nav-column:nth-child(2) .nav-icon {
            background: #007bff;
        }

        .nav-column:nth-child(2) .nav-text {
            color: #007bff;
        }

        /* 第三列绿色 */
        .nav-column:nth-child(3) .nav-icon {
            background: #28a745;
        }

        .nav-column:nth-child(3) .nav-text {
            color: #28a745;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 600;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 绿色长条 */
        .green-bar {
            width: 375px;
            height: 20px;
            background: #039e6d;
            display: flex;
            align-items: center;
            padding: 0 8px;
        }

        .green-bar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .green-bar-left {
            display: flex;
            align-items: center;
        }

        .green-bar-right {
            display: flex;
            align-items: center;
        }

        .home-icon {
            width: auto;
            height: 12px;
            margin-right: 3px;
        }

        .green-bar-clickable {
            color: white;
            font-size: 9px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            margin-right: 3px;
        }

        .green-bar-clickable:hover {
            text-decoration: underline;
        }

        .green-bar-text {
            color: white;
            font-size: 9px;
            font-weight: 600;
            margin-right: 3px;
        }

        .post-icon {
            width: auto;
            height: 12px;
            cursor: pointer;
        }

        .post-icon:hover {
            opacity: 0.8;
        }

        .user-info {
            color: white;
            font-size: 9px;
            font-weight: 600;
        }

        .user-link {
            color: white;
            font-size: 9px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            margin: 0 2px;
        }

        .user-link:hover {
            text-decoration: underline;
        }

        /* 登录表单 */
        .login-form {
            width: 375px;
            padding: 4px 8px;
            background: white;
            border-radius: 3px;
            margin-top: 1px;
        }

        .form-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 4px;
            flex-wrap: wrap;
        }

        .form-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .form-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .form-label {
            font-size: 9px;
            font-weight: 600;
            color: #333;
            white-space: nowrap;
        }

        .form-input {
            padding: 2px 4px;
            border: 1px solid #ccc;
            border-radius: 2px;
            font-size: 9px;
            width: 50px;
            outline: none;
        }

        .form-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 1px rgba(0, 123, 255, 0.3);
        }

        .form-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
        }

        .form-btn:hover {
            background: linear-gradient(135deg, #e0a800 0%, #f8f9fa 100%);
            transform: translateY(-1px);
        }

        .search-input {
            padding: 2px 4px;
            border: 1px solid #ccc;
            border-radius: 2px;
            font-size: 9px;
            width: 60px;
            outline: none;
        }

        .search-input:focus {
            border-color: #28a745;
            box-shadow: 0 0 1px rgba(40, 167, 69, 0.3);
        }

        .search-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
        }

        .search-btn:hover {
            background: linear-gradient(135deg, #e0a800 0%, #f8f9fa 100%);
            transform: translateY(-1px);
        }

        /* 滚动公告 */
        .notice-scroll {
            width: 375px;
            height: auto;
            background: white;
            overflow: hidden;
            display: flex;
            align-items: center;
            margin-top: 0;
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0 0 3px 3px;
            padding: 8px 0;
        }

        .notice-content {
            color: #b91c1c;
            font-size: 16px;
            font-weight: 900;
            white-space: nowrap;
            animation: scroll-left 30s linear infinite;
            padding-left: 100%;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        /* 右下角浮动菜单 */
        .floating-menu {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .menu-item {
            width: 45px;
            height: 45px;
            background: #000000cc;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);
        }

        /* 内容容器 */
        .content-container {
            width: 375px;
            min-height: 200px;
            background: white;
            border: 1px solid #28a745;
            margin-top: 0;
            padding: 10px;
        }

        /* 公告标题 */
        .announcement-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .announcement-icon {
            width: auto;
            height: 16px;
            margin-right: 6px;
        }

        .announcement-text {
            background: #ADEAEA;
            color: rgb(75, 0, 130);
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            flex: 1;
        }

        /* 充币弹窗遮罩 */
        .recharge-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 2000;
        }

        /* 充币弹窗 */
        .recharge-modal {
            width: 100%;
            background: white;
            border-radius: 12px 12px 0 0;
            padding: 20px;
            text-align: center;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .recharge-modal.show {
            transform: translateY(0);
        }

        .close-recharge {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .recharge-qr {
            width: 200px;
            height: auto;
            margin: 10px 0 15px 0;
        }

        .recharge-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-top: 10px;
        }









        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }

            .red-section {
                width: 100%;
            }

            .nav-section {
                width: 100%;
            }

            .green-bar {
                width: 100%;
            }

            .login-form {
                width: 100%;
            }

            .notice-scroll {
                width: 100%;
            }

            .content-container {
                width: 100%;
            }


        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 顶部图片区域 -->
        <section class="hero-section">
            <img border="0" src="/images/6677168.gif" width="100%" height="180">
        </section>

        <!-- 标签区域 -->
        <section class="tabs-section">
            <div class="tabs-container">
                <button class="tab-item tab-1 active" onclick="selectTab(1)">私彩1</button>
                <button class="tab-item tab-2" onclick="selectTab(2)">新澳</button>
                <button class="tab-item tab-3" onclick="selectTab(3)">香港</button>
                <button class="tab-item tab-4" onclick="selectTab(4)">老澳</button>
            </div>
        </section>

        <!-- 开奖信息区域 -->
        <section class="lottery-info">
            <div class="lottery-text" id="lotteryText">
                私彩 第<span class="period-number">200</span>期开奖结果:
            </div>
            <button class="record-btn" onclick="showRecord()">开奖记录</button>
        </section>

        <!-- 号码球区域 -->
        <section class="balls-section">
            <div class="balls-container" id="ballsContainer">
                <!-- 7个号码球将通过JavaScript动态生成 -->
            </div>
        </section>

        <!-- 开奖时间区域 -->
        <section class="draw-time-section">
            <div class="draw-time-text" id="drawTimeText">
                第<span class="period-number">200</span>期开奖时间07月28日 周一21点32分
            </div>
            <button class="refresh-btn" onclick="refreshData()">刷新</button>
        </section>
    </div>

    <!-- 红色区域 -->
    <div class="red-section">
        <div class="red-content">
            <img src="/images/shuangjiantou.gif" alt="箭头" class="arrow-icon arrow-left">
            <span>六合宝典◆发帖有奖</span>
            <img src="/images/shuangjiantou.gif" alt="箭头" class="arrow-icon">
        </div>
        <div class="yellow-notice">
            紧急通知↓长时间不回信息请添加新客服<br>
            ↓↓↓↓↓↓↓↓<br>
            充值客服微信号：xxxxx<br>
            充值客服QQ号：xxxxxxx<br>
            <span class="small-text">通知：①新用户注册联系管理员免费赠送88个铜币<br>
            ②充值铜币（支付宝 微信 收款码支付）<br>
            ③您需要查看的资料下方，点击打赏高手，即可查看到<br>
            高手发布的资料</span>
        </div>
    </div>

    <!-- 导航区域 -->
    <div class="nav-section">
        <div class="nav-grid">
            <div class="nav-column">
                <a href="#" class="nav-item" onclick="navigateTo('管家婆论坛')">
                    <div class="nav-icon">管</div>
                    <div class="nav-text">管家婆论坛</div>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('论坛4')">
                    <div class="nav-icon">4</div>
                    <div class="nav-text">论坛4</div>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('论坛7')">
                    <div class="nav-icon">7</div>
                    <div class="nav-text">论坛7</div>
                </a>
            </div>
            <div class="nav-column">
                <a href="#" class="nav-item" onclick="navigateTo('白娘子论坛')">
                    <div class="nav-icon">白</div>
                    <div class="nav-text">白娘子论坛</div>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('论坛5')">
                    <div class="nav-icon">5</div>
                    <div class="nav-text">论坛5</div>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('论坛8')">
                    <div class="nav-icon">8</div>
                    <div class="nav-text">论坛8</div>
                </a>
            </div>
            <div class="nav-column">
                <a href="#" class="nav-item" onclick="navigateTo('王牌高手论坛')">
                    <div class="nav-icon">王</div>
                    <div class="nav-text">王牌高手论坛</div>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('论坛6')">
                    <div class="nav-icon">6</div>
                    <div class="nav-text">论坛6</div>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('论坛9')">
                    <div class="nav-icon">9</div>
                    <div class="nav-text">论坛9</div>
                </a>
            </div>
        </div>
    </div>

    <!-- 绿色长条 -->
    <div class="green-bar">
        <div class="green-bar-content">
            <div class="green-bar-left">
                <img src="/images/home.gif" alt="首页" class="home-icon">
                <a href="#" class="green-bar-clickable" onclick="contactAdmin()">『澳门内部最准资料』</a>
                <span class="green-bar-text">☎ 论坛管理員：</span>
                <img src="/images/post.gif" alt="发帖" class="post-icon" onclick="postMessage()">
            </div>
            <div class="green-bar-right">
                <span class="user-info">您当前是游客：</span>
                <a href="#" class="user-link" onclick="showLogin()">登录</a>
                <span class="user-info">|</span>
                <a href="#" class="user-link" onclick="showRegister()">注册</a>
            </div>
        </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
        <div class="form-row">
            <div class="form-left">
                <label class="form-label">用户名:</label>
                <input type="text" class="form-input" placeholder="用户名">
                <label class="form-label">密码:</label>
                <input type="password" class="form-input" placeholder="密码">
                <button class="form-btn btn-login" onclick="handleLogin()">登录</button>
                <button class="form-btn btn-register" onclick="handleRegister()">注册</button>
            </div>
            <div class="form-right">
                <input type="text" class="search-input" placeholder="搜索">
                <button class="search-btn" onclick="handleSearch()">搜索</button>
            </div>
        </div>
    </div>

    <!-- 滚动公告 -->
    <div class="notice-scroll">
        <div class="notice-content">
            公告：下列出售贴分为两类，第一类《审核成功经过本公司所有高层审核，条件达到，十准七以上》第二类《验证成功是经过进站看资料的所有彩友共同见证，条件达到，八准五至六，这根据每一条资料的难度而定》有任何疑问题请添加客服《微信:xxxxx》
        </div>
    </div>

    <!-- 内容容器 -->
    <div class="content-container">
        <!-- 公告标题 -->
        <div class="announcement-header">
            <img src="/images/headtopic_3.gif" alt="公告图标" class="announcement-icon">
            <div class="announcement-text">
                ★ ★ ★ 充值请联系最新管理员微信号：xxxxxx ★ ★ ★
            </div>
        </div>

        <!-- 第二条公告 -->
        <div class="announcement-header">
            <img src="/images/headtopic_3.gif" alt="公告图标" class="announcement-icon">
            <div class="announcement-text">
                ★★★避免不必要的误会，请认真看完论坛规则★★★
            </div>
        </div>
        <!-- 这里等待添加更多内容 -->
    </div>

    <!-- 充币弹窗遮罩 -->
    <div class="recharge-overlay" id="rechargeOverlay">
        <div class="recharge-modal">
            <span class="close-recharge" onclick="closeRecharge()">&times;</span>
            <img src="/images/11.png" alt="充币二维码" class="recharge-qr">
            <div class="recharge-title">扫码联系客服</div>
        </div>
    </div>

    <!-- 右下角浮动菜单 -->
    <div class="floating-menu">
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('注册')">注册</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('充币')">充币</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('优惠')">优惠</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('介绍')">介绍</a>
    </div>

    <script>
        function selectTab(tabNumber) {
            // 移除所有标签的active类
            const allTabs = document.querySelectorAll('.tab-item');
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 给点击的标签添加active类
            const selectedTab = document.querySelector(`.tab-${tabNumber}`);
            selectedTab.classList.add('active');

            // 更新开奖信息文本
            const lotteryText = document.getElementById('lotteryText');
            const lotteryNames = {
                1: '私彩',
                2: '新澳',
                3: '香港',
                4: '老澳'
            };

            const periodNumbers = {
                1: '200',
                2: '156',
                3: '089',
                4: '234'
            };

            lotteryText.innerHTML = `${lotteryNames[tabNumber]} 第<span class="period-number">${periodNumbers[tabNumber]}</span>期开奖结果:`;

            // 更新号码球
            updateBalls(tabNumber);

            // 更新开奖时间
            updateDrawTime(tabNumber);
        }

        function updateBalls(tabNumber) {
            const ballsContainer = document.getElementById('ballsContainer');
            const ballsData = {
                1: [
                    { number: '08', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '15', zodiac: '兔', element: '木', color: 'green' },
                    { number: '23', zodiac: '猪', element: '火', color: 'red' },
                    { number: '31', zodiac: '马', element: '火', color: 'red' },
                    { number: '42', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '07', zodiac: '牛', element: '土', color: 'green' },
                    { number: '19', zodiac: '羊', element: '土', color: 'red' }
                ],
                2: [
                    { number: '03', zodiac: '兔', element: '木', color: 'green' },
                    { number: '12', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '25', zodiac: '龙', element: '土', color: 'red' },
                    { number: '34', zodiac: '虎', element: '木', color: 'green' },
                    { number: '41', zodiac: '马', element: '火', color: 'red' },
                    { number: '06', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '18', zodiac: '鸡', element: '金', color: 'blue' }
                ],
                3: [
                    { number: '01', zodiac: '猪', element: '火', color: 'red' },
                    { number: '14', zodiac: '虎', element: '木', color: 'green' },
                    { number: '27', zodiac: '兔', element: '木', color: 'green' },
                    { number: '33', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '45', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '09', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '22', zodiac: '虎', element: '木', color: 'green' }
                ],
                4: [
                    { number: '05', zodiac: '马', element: '火', color: 'red' },
                    { number: '16', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '28', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '37', zodiac: '猪', element: '火', color: 'red' },
                    { number: '44', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '11', zodiac: '猪', element: '火', color: 'red' },
                    { number: '29', zodiac: '马', element: '火', color: 'red' }
                ]
            };

            const balls = ballsData[tabNumber];
            ballsContainer.innerHTML = '';

            balls.forEach((ball, index) => {
                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ball.color}.png" alt="${ball.color} ball" class="ball-image">
                        <div class="ball-number">${ball.number}</div>
                    </div>
                    <div class="ball-info">${ball.zodiac}/${ball.element}</div>
                `;
                ballsContainer.appendChild(ballItem);

                // 在第6个球后添加加号分隔符
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });
        }

        function updateDrawTime(tabNumber) {
            const drawTimeText = document.getElementById('drawTimeText');
            const drawTimes = {
                1: { period: '200', time: '开奖时间07月28日 周一21点32分' },
                2: { period: '156', time: '开奖时间07月28日 周一20点15分' },
                3: { period: '089', time: '开奖时间07月28日 周一19点45分' },
                4: { period: '234', time: '开奖时间07月28日 周一22点10分' }
            };

            const drawTime = drawTimes[tabNumber];
            drawTimeText.innerHTML = `第<span class="period-number">${drawTime.period}</span>期${drawTime.time}`;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateBalls(1); // 默认显示私彩的球
            updateDrawTime(1); // 默认显示私彩的开奖时间
        });

        function refreshData() {
            // 获取当前选中的标签
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;

            // 重新加载数据
            updateBalls(tabNumber);
            updateDrawTime(tabNumber);

            // 简单的刷新提示
            alert('数据已刷新');
        }

        function showRecord() {
            alert('开奖记录功能');
        }

        function navigateTo(forumName) {
            alert('跳转到：' + forumName);
        }

        function contactAdmin() {
            alert('澳门内部最准资料');
        }

        function postMessage() {
            alert('发帖功能');
        }

        function showLogin() {
            window.location.href = '/index/index/login';
        }

        function showRegister() {
            window.location.href = '/index/index/register';
        }

        function handleLogin() {
            window.location.href = '/index/index/login';
        }

        function handleRegister() {
            window.location.href = '/index/index/register';
        }

        function handleSearch() {
            const searchText = document.querySelector('.search-input').value;
            if (searchText) {
                alert('搜索：' + searchText);
            } else {
                alert('请输入搜索内容');
            }
        }

        function handleMenuClick(action) {
            if (action === '注册') {
                window.location.href = '/index/index/register';
            } else if (action === '充币') {
                openRecharge();
            } else if (action === '优惠') {
                window.location.href = '/index/index/promotion';
            } else if (action === '介绍') {
                window.location.href = '/index/index/about';
            } else {
                alert(action + '功能');
            }
        }

        function openRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            overlay.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // 防止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function closeRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                overlay.style.display = 'none';
                // 恢复页面滚动
                document.body.style.overflow = '';
            }, 300);
        }

        // 点击空白区域关闭弹窗
        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.getElementById('rechargeOverlay');
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeRecharge();
                }
            });
        });
    </script>
</body>
</html>
