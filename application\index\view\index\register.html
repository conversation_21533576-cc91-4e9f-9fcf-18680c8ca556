<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 注册页面 */
        .register-page {
            width: 300px;
            background: white;
            padding: 30px;
        }

        .register-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 25px;
            color: #333;
        }

        .register-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .required {
            color: red;
        }

        .register-input {
            padding: 8px 10px;
            border: 1px solid #ccc;
            font-size: 14px;
            outline: none;
        }

        .register-input:focus {
            border-color: #007bff;
        }

        .register-submit {
            padding: 10px 20px;
            background: #ffc107;
            color: #333;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
        }

        .register-submit:hover {
            background: #e0a800;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }
            
            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 注册页面 -->
        <div class="register-page">
            <div class="register-title">用户注册</div>
            <form class="register-form" onsubmit="submitRegister(event)">
                <div class="form-group">
                    <label>用户名 <span class="required">*</span></label>
                    <input type="text" class="register-input" required>
                </div>
                <div class="form-group">
                    <label>密&nbsp;&nbsp;&nbsp;&nbsp;码 <span class="required">*</span></label>
                    <input type="password" class="register-input" required>
                </div>
                <div class="form-group">
                    <label>确认密码 <span class="required">*</span></label>
                    <input type="password" class="register-input" required>
                </div>
                <button type="submit" class="register-submit">提交注册</button>
            </form>
            <div class="back-link">
                <a href="index.html">返回首页</a>
            </div>
        </div>
    </div>

    <script>
        function submitRegister(event) {
            event.preventDefault();
            const inputs = document.querySelectorAll('.register-input');
            const username = inputs[0].value;
            const password = inputs[1].value;
            const confirmPassword = inputs[2].value;
            
            if (password !== confirmPassword) {
                alert('密码和确认密码不一致');
                return;
            }
            
            alert('注册成功：用户名 ' + username);
            // 可以在这里添加跳转到登录页面或首页的逻辑
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
