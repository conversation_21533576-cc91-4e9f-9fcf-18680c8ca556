<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\admin\model\HomepageContent;

/**
 * 首页接口
 */
class Homepage extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 获取首页内容
     */
    public function index()
    {
        try {
            // 获取顶部图片
            $banner = HomepageContent::getBanner();
            
            // 获取公告列表
            $notices = HomepageContent::getNotices();
            
            $data = [
                'banner' => $banner ? [
                    'id' => $banner->id,
                    'title' => $banner->title,
                    'image' => $banner->image,
                    'url' => $banner->url
                ] : null,
                'notices' => []
            ];
            
            if ($notices) {
                foreach ($notices as $notice) {
                    $data['notices'][] = [
                        'id' => $notice->id,
                        'title' => $notice->title,
                        'content' => $notice->content,
                        'url' => $notice->url
                    ];
                }
            }
            
            $this->success('获取成功', $data);
            
        } catch (\Exception $e) {
            $this->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取顶部图片
     */
    public function banner()
    {
        try {
            $banner = HomepageContent::getBanner();
            
            if (!$banner) {
                $this->error('暂无顶部图片');
            }
            
            $data = [
                'id' => $banner->id,
                'title' => $banner->title,
                'image' => $banner->image,
                'url' => $banner->url
            ];
            
            $this->success('获取成功', $data);
            
        } catch (\Exception $e) {
            $this->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取公告列表
     */
    public function notices()
    {
        try {
            $notices = HomepageContent::getNotices();
            
            $data = [];
            if ($notices) {
                foreach ($notices as $notice) {
                    $data[] = [
                        'id' => $notice->id,
                        'title' => $notice->title,
                        'content' => $notice->content,
                        'url' => $notice->url
                    ];
                }
            }
            
            $this->success('获取成功', $data);
            
        } catch (\Exception $e) {
            $this->error('获取失败：' . $e->getMessage());
        }
    }
}
