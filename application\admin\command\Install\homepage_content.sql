-- 首页内容管理表
CREATE TABLE `fa_homepage_content` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` enum('banner','notice') NOT NULL DEFAULT 'banner' COMMENT '内容类型:banner=顶部图片,notice=公告',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
  `content` text COMMENT '内容',
  `image` varchar(255) DEFAULT '' COMMENT '图片路径',
  `url` varchar(255) DEFAULT '' COMMENT '链接地址',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='首页内容管理表';

-- 插入默认数据
INSERT INTO `fa_homepage_content` (`type`, `title`, `content`, `image`, `sort`, `status`, `createtime`, `updatetime`) VALUES
('banner', '首页顶部图片', '', '/images/6677168.gif', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('notice', '充值公告', '★ ★ ★ 充值请联系最新管理员微信号：xxxxxx ★ ★ ★', '', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('notice', '论坛规则', '★★★避免不必要的误会，请认真看完论坛规则★★★', '', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
